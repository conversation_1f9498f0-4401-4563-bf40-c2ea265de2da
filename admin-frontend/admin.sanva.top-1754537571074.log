api.ts:21 Environment check: Object
api.ts:28 Getting API config
api.ts:68 Production build detected
api.ts:81 Final API config: Object
api.ts:28 Getting API config
api.ts:68 Production build detected
api.ts:81 Final API config: Object
api.ts:28 Getting API config
api.ts:68 Production build detected
api.ts:81 Final API config: Object
useVersionCheck.ts:166 Version check failed (attempt 1): Error: 版本检测完成
    at Object.checkVersion (system.ts:138:11)
    at async useVersionCheck.ts:124:22
（匿名） @ useVersionCheck.ts:166
useVersionCheck.ts:166 Version check failed (attempt 2): Error: 版本检测完成
    at Object.checkVersion (system.ts:138:11)
    at async useVersionCheck.ts:124:22
（匿名） @ useVersionCheck.ts:166
useVersionCheck.ts:166 Version check failed (attempt 3): Error: 版本检测完成
    at Object.checkVersion (system.ts:138:11)
    at async useVersionCheck.ts:124:22
（匿名） @ useVersionCheck.ts:166
useApi.ts:72 API执行错误: {error: Error: PlanExecutor error during aggregation :: caused by :: $substrBytes:  Invalid range, ending i…, errorMessage: 'PlanExecutor error during aggregation :: caused by…ding index is in the middle of a UTF-8 character.', args: Array(1), apiFunction: 'getSessions', stack: 'Error: PlanExecutor error during aggregation :: ca…dmin.sanva.top/assets/index-3c37f4aa.js:75:181488'}
（匿名） @ useApi.ts:72
await in （匿名）
（匿名） @ SessionList.tsx:58
（匿名） @ SessionList.tsx:68
ql @ react-dom.production.min.js:243
Sn @ react-dom.production.min.js:285
C1 @ react-dom.production.min.js:282
Mr @ react-dom.production.min.js:280
nh @ react-dom.production.min.js:272
Nr @ react-dom.production.min.js:127
（匿名） @ react-dom.production.min.js:266
SessionList.tsx:62 加载会话列表失败: Error: PlanExecutor error during aggregation :: caused by :: $substrBytes:  Invalid range, ending index is in the middle of a UTF-8 character.
    at getSessions (chat.ts:115:11)
    at async useApi.ts:29:24
    at async SessionList.tsx:58:22
（匿名） @ SessionList.tsx:62
await in （匿名）
（匿名） @ SessionList.tsx:68
ql @ react-dom.production.min.js:243
Sn @ react-dom.production.min.js:285
C1 @ react-dom.production.min.js:282
Mr @ react-dom.production.min.js:280
nh @ react-dom.production.min.js:272
Nr @ react-dom.production.min.js:127
（匿名） @ react-dom.production.min.js:266
useApi.ts:72 API执行错误: {error: Error: PlanExecutor error during aggregation :: caused by :: $substrBytes:  Invalid range, ending i…, errorMessage: 'PlanExecutor error during aggregation :: caused by…ding index is in the middle of a UTF-8 character.', args: Array(1), apiFunction: 'getSessions', stack: 'Error: PlanExecutor error during aggregation :: ca…dmin.sanva.top/assets/index-3c37f4aa.js:75:181488'}
（匿名） @ useApi.ts:72
await in （匿名）
（匿名） @ SessionList.tsx:58
（匿名） @ SessionList.tsx:68
ql @ react-dom.production.min.js:243
Sn @ react-dom.production.min.js:285
C1 @ react-dom.production.min.js:282
Mr @ react-dom.production.min.js:280
nh @ react-dom.production.min.js:272
Nr @ react-dom.production.min.js:127
（匿名） @ react-dom.production.min.js:266
SessionList.tsx:62 加载会话列表失败: Error: PlanExecutor error during aggregation :: caused by :: $substrBytes:  Invalid range, ending index is in the middle of a UTF-8 character.
    at getSessions (chat.ts:115:11)
    at async useApi.ts:29:24
    at async SessionList.tsx:58:22
（匿名） @ SessionList.tsx:62
await in （匿名）
（匿名） @ SessionList.tsx:68
ql @ react-dom.production.min.js:243
Sn @ react-dom.production.min.js:285
C1 @ react-dom.production.min.js:282
Mr @ react-dom.production.min.js:280
nh @ react-dom.production.min.js:272
Nr @ react-dom.production.min.js:127
（匿名） @ react-dom.production.min.js:266
useApi.ts:72 API执行错误: {error: Error: PlanExecutor error during aggregation :: caused by :: $substrBytes:  Invalid range, ending i…, errorMessage: 'PlanExecutor error during aggregation :: caused by…ding index is in the middle of a UTF-8 character.', args: Array(1), apiFunction: 'getSessions', stack: 'Error: PlanExecutor error during aggregation :: ca…dmin.sanva.top/assets/index-3c37f4aa.js:75:181488'}
（匿名） @ useApi.ts:72
await in （匿名）
（匿名） @ SessionList.tsx:58
（匿名） @ SessionList.tsx:68
ql @ react-dom.production.min.js:243
Sn @ react-dom.production.min.js:285
C1 @ react-dom.production.min.js:282
Mr @ react-dom.production.min.js:280
nh @ react-dom.production.min.js:272
Nr @ react-dom.production.min.js:127
（匿名） @ react-dom.production.min.js:266
SessionList.tsx:62 加载会话列表失败: Error: PlanExecutor error during aggregation :: caused by :: $substrBytes:  Invalid range, ending index is in the middle of a UTF-8 character.
    at getSessions (chat.ts:115:11)
    at async useApi.ts:29:24
    at async SessionList.tsx:58:22
（匿名） @ SessionList.tsx:62
await in （匿名）
（匿名） @ SessionList.tsx:68
ql @ react-dom.production.min.js:243
Sn @ react-dom.production.min.js:285
C1 @ react-dom.production.min.js:282
Mr @ react-dom.production.min.js:280
nh @ react-dom.production.min.js:272
Nr @ react-dom.production.min.js:127
（匿名） @ react-dom.production.min.js:266
